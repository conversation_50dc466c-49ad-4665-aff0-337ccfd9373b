use rocket::{post, serde::json::<PERSON><PERSON>};
use serde::Deserialize;

use crate::tickets::TICKETS;

#[derive(Debug, Deserialize)]
pub struct WebhookPayload<'a> {
    message_id: &'a str,
    ticket_id: &'a str,
    message: &'a str,
    user_id: &'a str,
    user_name: &'a str,
    user_email: &'a str,
    contact_id: &'a str,
    contact_name: &'a str,
    contact_email: Option<&'a str>,
    contact_identifier: Option<&'a str>,
    channel_id: &'a str,
}

#[post("/webhook", data = "<payload>")]
pub async fn handler<'a>(payload: Json<WebhookPayload<'a>>) {
    println!("recv /webhook with payload: {:?}", payload);

    if let Some(contact_identifier) = payload.contact_identifier {
        if contact_identifier.starts_with("custom-") {
            if let Ok(contact_identifier) = contact_identifier.parse::<u64>() {
                let _ticket = TICKETS.fetch(contact_identifier);
                // TODO: Implement webhook handling logic
            }
        }
    }
}
